# Project Creation Questionnaire Flow

## Overview
The new project creation flow includes a user type-casting questionnaire that helps personalize the experience while the workspace loads in the background. This replaces the simple loading screen with an interactive questionnaire.

## Flow Description

### 1. Project Idea Input
- User enters their project idea in the dashboard or landing page
- System stores the idea text and initiates the questionnaire flow
- User is redirected to `/projects/create` page

### 2. Questionnaire Steps
The questionnaire consists of 4 questions designed to understand the user's background and project context:

#### Question 1: First-time Founder
- **Question**: "Are you a first-time founder?"
- **Subtitle**: "This helps us tune the tone of our agents"
- **Type**: <PERSON><PERSON><PERSON> (Yes/No buttons)
- **Purpose**: Determines the communication style and guidance level

#### Question 2: Project Stage
- **Question**: "What stage are you at?"
- **Subtitle**: "Understanding your current progress"
- **Type**: Multiple choice
- **Options**:
  - Just an Idea
  - Building
  - Launched & Testing
  - Generating Revenue

#### Question 3: Time Investment
- **Question**: "How long have you been working on this?"
- **Subtitle**: "This helps us understand your timeline"
- **Type**: Multiple choice
- **Options**:
  - Less than 1 day
  - 1-7 days
  - 7-30 days
  - 30-90 days
  - 90+ days

#### Question 4: Project Name
- **Question**: "What would you like to name your project?"
- **Subtitle**: "Choose a name that represents your vision"
- **Type**: Text input
- **Purpose**: Sets the project name for creation

### 3. Background Processing
- While user answers questions, the system prepares for project creation
- Questionnaire data is stored in Zustand store
- Project creation begins immediately after questionnaire completion

### 4. Project Creation
- System uses questionnaire data to personalize the project
- Progress animation shows with rotating logo and green glow
- Real-time progress updates via Socket.IO (or mock stream)
- Completion animation with expanding circle transition

## Technical Implementation

### State Management
```typescript
interface QuestionnaireData {
  isFirstTimeFounder: boolean | null;
  stage: 'idea' | 'building' | 'launched' | 'revenue' | null;
  timeWorking: '<1day' | '1-7days' | '7-30days' | '30-90days' | '90+days' | null;
  projectName: string;
}
```

### Key Components
- `ProjectCreationQuestionnaire.tsx` - Main questionnaire component
- `ProjectCreationAnimation.tsx` - Handles questionnaire and creation flow
- `projectCreationStore.ts` - Zustand store for state management

### Flow Steps
1. **Idle** - Initial state
2. **Questionnaire** - User answering questions
3. **Creating** - Project creation in progress
4. **Expanding** - Completion animation
5. **Completed** - Project ready

## User Experience Features

### Progress Indicator
- Visual progress bar showing question completion (1/4, 2/4, etc.)
- Percentage indicator
- Smooth animations between questions

### Navigation
- Previous/Next buttons with proper validation
- Disabled states for incomplete answers
- Different button text for final question ("Create Project")

### Validation
- Required fields must be completed before proceeding
- Text input requires non-empty project name
- Visual feedback for selected options

### Animations
- Smooth transitions between questions
- Card-based layout with hover effects
- Framer Motion animations for state changes
- Background pattern with noise texture

## Customization Options

### Question Configuration
Questions are defined in a configuration array, making it easy to:
- Add new questions
- Modify existing questions
- Change question order
- Update validation rules

### Styling
- Consistent with app design system
- Dark/light mode support
- Responsive design for mobile
- Placeholder for custom illustrations

### Integration
- Works with existing project creation flow
- Compatible with Socket.IO real-time updates
- Supports mock data for development
- Integrates with analytics tracking

## Future Enhancements

### Planned Features
1. **Custom Illustrations** - Replace placeholder with SVG illustrations
2. **Conditional Questions** - Show different questions based on previous answers
3. **Progress Persistence** - Save progress if user navigates away
4. **Analytics Integration** - Track questionnaire completion rates
5. **A/B Testing** - Test different question variations

### Personalization Opportunities
- Customize project templates based on stage
- Adjust AI agent tone based on founder experience
- Provide relevant resources based on time investment
- Suggest project structure based on questionnaire data

## Configuration

### Environment Variables
No additional environment variables required for questionnaire flow.

### Feature Flags
The questionnaire can be enabled/disabled by modifying the project creation flow in `ProjectInputSection.tsx`.

### Mock Data
Development uses mock event streaming for project creation progress. Switch to Socket.IO for production real-time updates.

## Testing

### Manual Testing Steps
1. Navigate to dashboard or landing page
2. Enter a project idea
3. Click "Generate Project" or similar button
4. Complete all questionnaire steps
5. Verify project creation starts
6. Check project appears in dashboard

### Edge Cases
- Empty project name handling
- Navigation between questions
- Browser refresh during questionnaire
- Network interruption during creation

## Accessibility

### Features Implemented
- Keyboard navigation support
- Screen reader friendly labels
- High contrast mode compatibility
- Focus management between questions
- ARIA labels for progress indicators

### Future Improvements
- Voice input for project name
- Keyboard shortcuts for common actions
- Enhanced screen reader announcements
- Better mobile accessibility
