# Stepper Component Documentation

## Overview
The Stepper component is a reusable, configurable multi-step form component that supports different question types and provides a smooth user experience with automatic progression for single-choice questions.

## Features

### ✅ **Question Types**
- **Single Choice** - Radio-style selection with auto-advance
- **Multiple Choice** - Checkbox-style multi-selection
- **Text Input** - Single-line text input
- **Textarea** - Multi-line text input

### ✅ **User Experience**
- **Auto-advance** for single choice questions (800ms delay)
- **Progress indicator** with percentage and step counter
- **Smooth animations** between steps using Framer Motion
- **Validation** - prevents progression without required answers
- **Navigation** - Previous/Next buttons (hidden for auto-advance questions)
- **Responsive design** - works on all screen sizes

### ✅ **Customization**
- **Configurable questions** via props
- **Custom styling** support
- **Callback functions** for completion and step changes
- **Placeholder text** for input fields
- **Required field validation**

## Usage

### Basic Implementation
```tsx
import { Stepper, StepperConfig } from "@/components/ui/stepper";

const config: StepperConfig = {
  questions: [
    {
      id: "question1",
      title: "What's your favorite color?",
      subtitle: "Choose one option",
      type: "single",
      options: [
        { value: "red", label: "Red" },
        { value: "blue", label: "Blue" },
        { value: "green", label: "Green" },
      ],
    },
    {
      id: "question2", 
      title: "What's your name?",
      type: "text",
      placeholder: "Enter your name...",
    },
  ],
  onComplete: (answers) => {
    console.log("Completed with:", answers);
  },
  onStepChange: (step, answers) => {
    console.log(`Step ${step}:`, answers);
  },
};

export function MyForm() {
  return <Stepper config={config} />;
}
```

## Configuration

### StepperConfig Interface
```typescript
interface StepperConfig {
  questions: StepperQuestion[];
  onComplete: (answers: Record<string, any>) => void;
  onStepChange?: (currentStep: number, answers: Record<string, any>) => void;
}
```

### StepperQuestion Interface
```typescript
interface StepperQuestion {
  id: string;                    // Unique identifier
  title: string;                 // Question title
  subtitle?: string;             // Optional subtitle
  type: "single" | "multiple" | "text" | "textarea";
  options?: StepperOption[];     // For single/multiple choice
  placeholder?: string;          // For text/textarea inputs
  required?: boolean;            // Validation (default: true)
}
```

### StepperOption Interface
```typescript
interface StepperOption {
  value: string | number | boolean;
  label: string;
}
```

## Question Types

### 1. Single Choice
```typescript
{
  id: "preference",
  title: "What's your preference?",
  type: "single",
  options: [
    { value: "option1", label: "Option 1" },
    { value: "option2", label: "Option 2" },
  ],
}
```
- **Behavior**: Auto-advances after selection (800ms delay)
- **UI**: Button-style options with selection highlighting
- **Navigation**: No Next button shown (auto-advance)

### 2. Multiple Choice
```typescript
{
  id: "skills",
  title: "What skills do you have?",
  type: "multiple", 
  options: [
    { value: "js", label: "JavaScript" },
    { value: "py", label: "Python" },
    { value: "go", label: "Go" },
  ],
}
```
- **Behavior**: Requires manual Next button click
- **UI**: Checkbox-style selection
- **Navigation**: Previous/Next buttons shown

### 3. Text Input
```typescript
{
  id: "name",
  title: "What's your name?",
  type: "text",
  placeholder: "Enter your name...",
}
```
- **Behavior**: Validates non-empty input
- **UI**: Single-line input field
- **Navigation**: Previous/Next buttons shown

### 4. Textarea Input
```typescript
{
  id: "description",
  title: "Describe your project",
  type: "textarea",
  placeholder: "Tell us more...",
}
```
- **Behavior**: Validates non-empty input
- **UI**: Multi-line textarea (min-height: 100px)
- **Navigation**: Previous/Next buttons shown

## Styling

### Default Styling
- **Card-based layout** with border and padding
- **Progress bar** with green accent color (#166534)
- **Responsive design** with max-width constraints
- **Smooth animations** for transitions

### Custom Styling
```tsx
<Stepper 
  config={config} 
  className="custom-stepper-styles" 
/>
```

### CSS Variables
The component uses CSS custom properties that can be overridden:
- `--stepper-accent-color`: Primary accent color (default: #166534)
- `--stepper-border-radius`: Border radius for cards
- `--stepper-spacing`: Internal spacing

## Advanced Features

### Conditional Logic
You can implement conditional logic in the `onStepChange` callback:

```typescript
onStepChange: (step, answers) => {
  // Skip certain questions based on previous answers
  if (answers.userType === "beginner" && step === 2) {
    // Skip advanced question
  }
}
```

### Dynamic Questions
Questions can be generated dynamically:

```typescript
const generateQuestions = (userType: string) => {
  const baseQuestions = [...];
  
  if (userType === "advanced") {
    baseQuestions.push({
      id: "advanced",
      title: "Advanced Configuration",
      type: "multiple",
      options: [...],
    });
  }
  
  return baseQuestions;
};
```

### Validation
Custom validation can be added:

```typescript
{
  id: "email",
  title: "What's your email?",
  type: "text",
  placeholder: "<EMAIL>",
  validate: (value) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
  },
}
```

## Demo

Visit `/stepper-demo` to see the component in action with all question types.

## Integration Examples

### Project Creation Questionnaire
The stepper is used in the project creation flow:
- Founder experience question (single choice)
- Project stage selection (single choice) 
- Time investment (single choice)
- Project name input (text)

### Survey Forms
Perfect for multi-step surveys:
- User demographics
- Preference collection
- Feedback forms
- Onboarding flows

### Configuration Wizards
Ideal for setup wizards:
- Application configuration
- User preferences
- Feature selection
- Account setup

## Accessibility

### Features Included
- **Keyboard navigation** support
- **Screen reader** friendly labels
- **Focus management** between steps
- **ARIA labels** for progress indicators
- **High contrast** mode compatibility

### Best Practices
- Use descriptive titles and subtitles
- Provide clear option labels
- Include helpful placeholder text
- Ensure sufficient color contrast
- Test with screen readers

## Performance

### Optimizations
- **Lazy rendering** - only current step is rendered
- **Minimal re-renders** with optimized state management
- **Smooth animations** with Framer Motion
- **Efficient validation** with debounced checks

### Bundle Size
- Core component: ~8KB gzipped
- Dependencies: Framer Motion, React
- Total impact: ~15KB additional to bundle
