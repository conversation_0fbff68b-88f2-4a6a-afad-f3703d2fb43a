"use client";

import { Logo } from "@/components/ui/logo";
import { Stepper, StepperConfig } from "@/components/ui/stepper";
import { useProjectCreationStream } from "@/lib/mockEventStream";
import { useProjectCreationStore } from "@/stores/projectCreationStore";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export function ProjectCreationQuestionnaire() {
  const {
    ideaText,
    setQuestionnaireAnswer,
    completeQuestionnaire,
    createdProject,
    questionnaireData,
    setCreatedProject,
    setCurrentProgress,
    setTotalSteps,
    incrementCompletedSteps,
    setCurrentStep,
  } = useProjectCreationStore();

  const [isCompleted, setIsCompleted] = useState(false);
  const [projectCreationStarted, setProjectCreationStarted] = useState(false);
  const router = useRouter();

  // Project creation stream hook
  const { startCreation, addEventListener, removeEventListener } =
    useProjectCreationStream();

  // Start project creation when project name is entered (not when component mounts)
  useEffect(() => {
    if (
      questionnaireData.projectName &&
      questionnaireData.projectName.trim() &&
      !projectCreationStarted
    ) {
      console.log(
        "Starting project creation with name:",
        questionnaireData.projectName
      );
      startCreation(ideaText, questionnaireData.projectName);
      setProjectCreationStarted(true);
    }
  }, [
    questionnaireData.projectName,
    ideaText,
    startCreation,
    projectCreationStarted,
  ]);

  // Set up event stream listener for project creation
  useEffect(() => {
    const handleStreamEvent = (event: CustomEvent) => {
      const { type, data } = event.detail;

      switch (type) {
        case "setup":
          setTotalSteps(data.totalSteps);
          break;
        case "progress":
          setCurrentProgress(data);
          break;
        case "step_complete":
          incrementCompletedSteps();
          break;
        case "complete":
          console.log("Project creation completed:", data);
          setCreatedProject({
            id: data.projectId,
            name: data.name,
            description: data.description,
          });
          setCurrentStep("completed");
          break;
        case "error":
          console.error("Project creation error:", data.error);
          break;
      }
    };

    addEventListener(handleStreamEvent);

    return () => {
      removeEventListener(handleStreamEvent);
    };
  }, [
    addEventListener,
    removeEventListener,
    setCreatedProject,
    setCurrentProgress,
    setTotalSteps,
    incrementCompletedSteps,
    setCurrentStep,
  ]);

  // Redirect when project is created and questionnaire is completed
  useEffect(() => {
    if (isCompleted && createdProject) {
      // Project is ready, redirect to dashboard
      setTimeout(() => {
        router.push("/user-dashboard");
      }, 2000); // Show success for 2 seconds
    }
  }, [isCompleted, createdProject, router]);

  const stepperConfig: StepperConfig = {
    questions: [
      {
        id: "isFirstTimeFounder",
        title: "Are you a first-time founder?",
        subtitle: "This helps us tune the tone of our agents",
        type: "single",
        options: [
          { value: true, label: "Yes" },
          { value: false, label: "No" },
        ],
      },
      {
        id: "stage",
        title: "What stage are you at?",
        subtitle: "Understanding your current progress",
        type: "single",
        options: [
          { value: "idea", label: "Just an Idea" },
          { value: "building", label: "Building" },
          { value: "launched", label: "Launched & Testing" },
          { value: "revenue", label: "Generating Revenue" },
        ],
      },
      {
        id: "timeWorking",
        title: "How long have you been working on this?",
        subtitle: "This helps us understand your timeline",
        type: "single",
        options: [
          { value: "<1day", label: "Less than 1 day" },
          { value: "1-7days", label: "1-7 days" },
          { value: "7-30days", label: "7-30 days" },
          { value: "30-90days", label: "30-90 days" },
          { value: "90+days", label: "90+ days" },
        ],
      },
      {
        id: "projectName",
        title: "What would you like to name your project?",
        subtitle: "Choose a name that represents your vision",
        type: "text",
        placeholder: "Enter your project name...",
      },
    ],
    onComplete: (answers: Record<string, any>) => {
      // Update questionnaire data in store
      Object.entries(answers).forEach(([key, value]) => {
        setQuestionnaireAnswer(key as any, value);
      });

      // Complete questionnaire and set completed state
      setIsCompleted(true);
      completeQuestionnaire();
    },
    onStepChange: (_currentStep: number, answers: Record<string, any>) => {
      // Update answers in store as user progresses
      Object.entries(answers).forEach(([key, value]) => {
        setQuestionnaireAnswer(key as any, value);
      });

      // Start project creation when project name is entered
      if (
        answers.projectName &&
        answers.projectName.trim() &&
        !projectCreationStarted
      ) {
        console.log(
          "Starting project creation with name:",
          answers.projectName
        );
        startCreation(ideaText, answers.projectName);
        setProjectCreationStarted(true);
      }
    },
  };

  // Show project creation animation if completed but project not ready
  if (isCompleted && !createdProject) {
    return (
      <div className="fixed inset-0 z-50 bg-background overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-muted/20" />

        {/* Noise Texture Overlay */}
        <div
          className="absolute inset-0 opacity-[0.02] dark:opacity-[0.04]"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
            backgroundSize: "256px 256px",
          }}
        />

        {/* Content */}
        <div className="relative z-10 flex items-center justify-center min-h-screen">
          <div className="text-center">
            {/* App Logo */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="mb-8"
            >
              <Logo
                size={64}
                animated={true}
                showText={false}
                className="mx-auto"
              />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="space-y-4"
            >
              <h2 className="text-2xl font-bold text-[#166534]">
                Creating your project...
              </h2>
              <p className="text-muted-foreground">
                We're setting up everything for you
              </p>
            </motion.div>
          </div>
        </div>
      </div>
    );
  }

  // Show success message when project is ready
  if (isCompleted && createdProject) {
    return (
      <div className="fixed inset-0 z-50 bg-background overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-muted/20" />

        {/* Noise Texture Overlay */}
        <div
          className="absolute inset-0 opacity-[0.02] dark:opacity-[0.04]"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
            backgroundSize: "256px 256px",
          }}
        />

        {/* Content */}
        <div className="relative z-10 flex items-center justify-center min-h-screen">
          <div className="text-center">
            {/* Success Icon */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{
                type: "spring",
                stiffness: 200,
                damping: 20,
              }}
              className="w-16 h-16 mx-auto mb-6 rounded-full bg-[#166534] flex items-center justify-center"
            >
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2 }}
                className="text-white text-2xl"
              >
                ✓
              </motion.div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="space-y-4"
            >
              <h2 className="text-2xl font-bold text-[#166534]">
                Your project is ready!
              </h2>
              <p className="text-muted-foreground">
                {createdProject.name} has been created successfully
              </p>
              <p className="text-sm text-muted-foreground">
                Redirecting to dashboard...
              </p>
            </motion.div>
          </div>
        </div>
      </div>
    );
  }

  return <Stepper config={stepperConfig} />;
}
